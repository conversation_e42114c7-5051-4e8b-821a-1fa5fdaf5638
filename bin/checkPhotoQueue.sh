#!/bin/bash

# 检查照片下载队列并发送邮件报告（包含历史数据对比）
# 基于你的查询示例: db.reso_photo_download_queue.find({priority:{$gt:35000}}).count()

# 邮件配置
RM_FROM="<EMAIL>"
RM_URL="https://ml1.realmaster.cc/send"

# 历史数据文件
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
HISTORY_FILE="$SCRIPT_DIR/../logs/photo_queue_history.json"

# 确保日志目录存在
mkdir -p "$(dirname "$HISTORY_FILE")"

# 参数处理
RM_TO="$1"
THRESHOLD="$2"

# 默认值
if [ -z "$RM_TO" ]; then
    RM_TO="<EMAIL>,<EMAIL>"
fi

if [ -z "$THRESHOLD" ]; then
    THRESHOLD=35000
fi

# MongoDB连接URI (生产环境RNI数据库)
MONGO_URI="***********************************************************************************************************************************************************"

echo "==================== 检查照片下载队列 ===================="
echo "优先级阈值: $THRESHOLD"
echo "收件人: $RM_TO"
echo "历史数据文件: $HISTORY_FILE"

# 执行MongoDB查询
echo "正在查询数据库..."

# 查询高优先级任务数量
HIGH_PRIORITY_COUNT=$(mongosh "$MONGO_URI" --quiet --eval "
db.reso_photo_download_queue.find({priority:{\$gt:$THRESHOLD}}).count()
")

# 查询总任务数量
TOTAL_COUNT=$(mongosh "$MONGO_URI" --quiet --eval "
db.reso_photo_download_queue.find().count()
")

# 检查查询结果
if [ $? -ne 0 ]; then
    echo "MongoDB查询失败!"
    exit 1
fi

echo "查询结果:"
echo "高优先级任务 (priority > $THRESHOLD): $HIGH_PRIORITY_COUNT"
echo "总任务数量: $TOTAL_COUNT"

# 保存当前数据到历史记录
CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')
CURRENT_TIMESTAMP=$(date +%s)

# 创建当前记录
CURRENT_RECORD=$(jq -n \
    --arg timestamp "$CURRENT_TIMESTAMP" \
    --arg datetime "$CURRENT_TIME" \
    --arg high_priority "$HIGH_PRIORITY_COUNT" \
    --arg total "$TOTAL_COUNT" \
    --arg threshold "$THRESHOLD" \
    '{
        timestamp: ($timestamp | tonumber),
        datetime: $datetime,
        high_priority: ($high_priority | tonumber),
        total: ($total | tonumber),
        threshold: ($threshold | tonumber)
    }')

# 读取历史数据，如果文件不存在则创建空数组
if [ -f "$HISTORY_FILE" ]; then
    HISTORY_DATA=$(cat "$HISTORY_FILE")
else
    HISTORY_DATA="[]"
fi

# 添加当前记录并保持最近10条记录
NEW_HISTORY=$(echo "$HISTORY_DATA" | jq --argjson current "$CURRENT_RECORD" '
    . + [$current] | sort_by(.timestamp) | .[-10:]
')

# 保存更新后的历史数据
echo "$NEW_HISTORY" > "$HISTORY_FILE"

echo "历史数据已更新"

# 生成历史数据表格
HISTORY_TABLE=""
HISTORY_COUNT=$(echo "$NEW_HISTORY" | jq 'length')

if [ "$HISTORY_COUNT" -gt 0 ]; then
    echo "生成历史数据表格..."

    # 生成表格头
    HISTORY_TABLE="<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; font-family: monospace;'>
    <thead style='background-color: #f0f0f0;'>
        <tr>
            <th>时间</th>
            <th>高优先级任务</th>
            <th>总任务数</th>
            <th>高优先级占比</th>
            <th>变化趋势</th>
        </tr>
    </thead>
    <tbody>"

    # 生成表格行
    for i in $(seq 0 $((HISTORY_COUNT - 1))); do
        RECORD=$(echo "$NEW_HISTORY" | jq -r ".[$i]")
        DATETIME=$(echo "$RECORD" | jq -r '.datetime')
        HIGH_PRIORITY=$(echo "$RECORD" | jq -r '.high_priority')
        TOTAL=$(echo "$RECORD" | jq -r '.total')

        # 计算占比
        if [ "$TOTAL" -gt 0 ]; then
            PERCENTAGE=$(echo "scale=2; $HIGH_PRIORITY * 100 / $TOTAL" | bc)
        else
            PERCENTAGE="0"
        fi

        # 计算变化趋势
        TREND=""
        if [ $i -gt 0 ]; then
            PREV_RECORD=$(echo "$NEW_HISTORY" | jq -r ".[$((i-1))]")
            PREV_HIGH_PRIORITY=$(echo "$PREV_RECORD" | jq -r '.high_priority')
            PREV_TOTAL=$(echo "$PREV_RECORD" | jq -r '.total')

            HIGH_DIFF=$((HIGH_PRIORITY - PREV_HIGH_PRIORITY))
            TOTAL_DIFF=$((TOTAL - PREV_TOTAL))

            if [ $HIGH_DIFF -gt 0 ]; then
                TREND="📈 +$HIGH_DIFF"
            elif [ $HIGH_DIFF -lt 0 ]; then
                TREND="📉 $HIGH_DIFF"
            else
                TREND="➡️ 0"
            fi

            if [ $TOTAL_DIFF -ne 0 ]; then
                TREND="$TREND (总:$TOTAL_DIFF)"
            fi
        else
            TREND="--"
        fi

        # 根据是否是最新记录设置样式
        if [ $i -eq $((HISTORY_COUNT - 1)) ]; then
            ROW_STYLE="style='background-color: #ffffcc; font-weight: bold;'"
        else
            ROW_STYLE=""
        fi

        HISTORY_TABLE="$HISTORY_TABLE
        <tr $ROW_STYLE>
            <td>$DATETIME</td>
            <td>$HIGH_PRIORITY</td>
            <td>$TOTAL</td>
            <td>$PERCENTAGE%</td>
            <td>$TREND</td>
        </tr>"
    done

    HISTORY_TABLE="$HISTORY_TABLE
    </tbody>
    </table>"
fi

# 计算统计信息
STATS=""
if [ "$HISTORY_COUNT" -gt 1 ]; then
    AVG_HIGH_PRIORITY=$(echo "$NEW_HISTORY" | jq '[.[].high_priority] | add / length | floor')
    MAX_HIGH_PRIORITY=$(echo "$NEW_HISTORY" | jq '[.[].high_priority] | max')
    MIN_HIGH_PRIORITY=$(echo "$NEW_HISTORY" | jq '[.[].high_priority] | min')

    AVG_TOTAL=$(echo "$NEW_HISTORY" | jq '[.[].total] | add / length | floor')
    MAX_TOTAL=$(echo "$NEW_HISTORY" | jq '[.[].total] | max')
    MIN_TOTAL=$(echo "$NEW_HISTORY" | jq '[.[].total] | min')

    STATS="<h4>📊 统计信息 (最近${HISTORY_COUNT}次检查)</h4>
    <ul>
        <li><strong>高优先级任务:</strong> 平均 $AVG_HIGH_PRIORITY, 最高 $MAX_HIGH_PRIORITY, 最低 $MIN_HIGH_PRIORITY</li>
        <li><strong>总任务数:</strong> 平均 $AVG_TOTAL, 最高 $MAX_TOTAL, 最低 $MIN_TOTAL</li>
    </ul>"
fi

# 判断是否需要告警
ALERT_NEEDED=false
ALERT_REASON=""

# 如果高优先级任务超过100个，发送告警
if [ "$HIGH_PRIORITY_COUNT" -gt 100 ]; then
    ALERT_NEEDED=true
    ALERT_REASON="高优先级任务数量过多 ($HIGH_PRIORITY_COUNT > 100)"
fi

# 如果总任务数量超过500000，发送告警
if [ "$TOTAL_COUNT" -gt 500000 ]; then
    ALERT_NEEDED=true
    if [ -n "$ALERT_REASON" ]; then
        ALERT_REASON="$ALERT_REASON; 总任务数量过多 ($TOTAL_COUNT > 500000)"
    else
        ALERT_REASON="总任务数量过多 ($TOTAL_COUNT > 500000)"
    fi
fi

if [ "$ALERT_NEEDED" = true ]; then
    EMAIL_SUBJECT="🚨 照片下载队列告警 - $(date '+%m/%d %H:%M')"
    rmText="<h2 style='color: red;'>🚨 照片下载队列告警</h2>
<p><strong>告警时间:</strong> $CURRENT_TIME</p>
<p><strong>告警原因:</strong> $ALERT_REASON</p>
<hr>
<h3>当前队列状态:</h3>
<ul>
<li><strong>高优先级任务 (priority > $THRESHOLD):</strong> <span style='color: red; font-weight: bold;'>$HIGH_PRIORITY_COUNT</span></li>
<li><strong>总任务数量:</strong> <span style='color: red; font-weight: bold;'>$TOTAL_COUNT</span></li>
</ul>
$STATS
<hr>
<h3>📈 历史趋势 (最近${HISTORY_COUNT}次检查)</h3>
$HISTORY_TABLE
<hr>
<p><strong>建议操作:</strong></p>
<ul>
<li>检查照片下载服务状态</li>
<li>查看服务器资源使用情况</li>
<li>考虑增加处理能力或清理队列</li>
</ul>
<p><em>此告警由自动监控脚本生成 - 每3小时检查一次</em></p>"
else
    EMAIL_SUBJECT="✅ 照片下载队列状态报告 - $(date '+%m/%d %H:%M')"
    rmText="<h2 style='color: green;'>✅ 照片下载队列状态正常</h2>
<p><strong>检查时间:</strong> $CURRENT_TIME</p>
<hr>
<h3>当前队列状态:</h3>
<ul>
<li><strong>高优先级任务 (priority > $THRESHOLD):</strong> $HIGH_PRIORITY_COUNT</li>
<li><strong>总任务数量:</strong> $TOTAL_COUNT</li>
</ul>
$STATS
<hr>
<h3>📈 历史趋势 (最近${HISTORY_COUNT}次检查)</h3>
$HISTORY_TABLE
<p><em>此报告由自动监控脚本生成 - 每3小时检查一次</em></p>"
fi

# 发送邮件
if [ ${#rmText} -gt 0 ] && [ ${#RM_TO} -gt 0 ]; then
  echo "======================= 发送邮件 ======================="
  
  mail=$(jq -n \
    --arg from "$RM_FROM" \
    --arg to "$RM_TO" \
    --arg subject "$EMAIL_SUBJECT" \
    --arg html "$rmText" \
    '{from: $from, to: $to, subject: $subject, html: $html}')
  
  # 将收件人转换为数组
  newEmail=$(jq -r '.to |= split(",")' <<< "${mail}")
  
  echo "发送邮件: $EMAIL_SUBJECT"
  
  # 发送邮件
  response=$(curl -X POST \
    --header "Content-Type: application/json" \
    --header "X-RM-Auth: RM" \
    -s \
    --data "$newEmail" \
    $RM_URL)
  
  if [ $? -eq 0 ]; then
    echo "邮件发送成功!"
    if [ "$ALERT_NEEDED" = true ]; then
        echo "⚠️  告警邮件已发送"
    else
        echo "ℹ️  状态报告已发送"
    fi
  else
    echo "邮件发送失败!"
    exit 1
  fi
fi

echo "======================= 检查完成 ======================="
exit 0
