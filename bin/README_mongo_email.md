# MongoDB查询邮件脚本使用说明

## 脚本概述

创建了完整的MongoDB监控和邮件通知系统，**自动从 `local.ini` 配置文件读取设置**：

1. `mongoQueryAndEmail.sh` - 通用的MongoDB查询邮件脚本
2. `checkPhotoQueue.sh` - 专门用于检查照片下载队列的监控脚本（**增强版，包含历史数据对比**）
3. `setupCrontab.sh` - 自动设置定时任务的脚本
4. `testPhotoQueueMonitor.sh` - 测试和验证功能的脚本
5. `testConfigReading.sh` - 测试配置文件读取功能的脚本

## 🔧 配置文件集成

所有脚本现在都会自动从 `local.ini` 配置文件读取设置：

### 自动读取的配置项：
- **邮件地址**: `contact.alertEmail = '<EMAIL>'`
- **MongoDB连接**: `dbs.rni.uri = "mongodb://..."`

### 配置优先级：
1. 命令行参数（最高优先级）
2. `local.ini` 配置文件
3. 脚本默认值（最低优先级）

## 脚本功能

### 1. mongoQueryAndEmail.sh (通用脚本)

**功能**: 执行自定义MongoDB查询并将结果通过邮件发送

**用法**:
```bash
./bin/mongoQueryAndEmail.sh [收件人邮箱] [MongoDB连接URI] [查询条件] [邮件主题]
```

**参数说明**:
- `收件人邮箱`: 邮件接收者，多个邮箱用逗号分隔 (默认: <EMAIL>,<EMAIL>)
- `MongoDB连接URI`: 数据库连接字符串 (默认: RNI测试数据库)
- `查询条件`: MongoDB查询条件，JSON格式 (默认: {priority:{$gt:35000}})
- `邮件主题`: 邮件标题 (默认: "MongoDB查询结果报告")

**示例**:
```bash
# 使用默认参数
./bin/mongoQueryAndEmail.sh

# 指定收件人
./bin/mongoQueryAndEmail.sh "<EMAIL>"

# 完整参数
./bin/mongoQueryAndEmail.sh "<EMAIL>" "mongodb://..." "{status:'pending'}" "待处理任务报告"
```

### 2. checkPhotoQueue.sh (专用监控脚本 - 增强版)

**功能**: 监控照片下载队列状态，包含历史数据对比和趋势分析

**新增特性**:
- 📊 **历史数据追踪**: 自动保存最近10次检查结果
- 📈 **趋势分析**: 显示变化趋势和统计信息
- 📧 **增强邮件**: 包含历史对比表格和趋势图表
- 💾 **数据持久化**: 历史数据保存在 `logs/photo_queue_history.json`

**用法**:
```bash
./bin/checkPhotoQueue.sh [收件人邮箱] [优先级阈值]
```

**参数说明**:
- `收件人邮箱`: 邮件接收者 (默认: <EMAIL>,<EMAIL>)
- `优先级阈值`: 高优先级任务的阈值 (默认: 35000)

**告警条件**:
- 高优先级任务数量 > 100
- 总任务数量 > 500000

**邮件内容包含**:
- 当前队列状态
- 历史趋势表格 (最近10次检查)
- 统计信息 (平均值、最大值、最小值)
- 变化趋势指示 (📈📉➡️)

**示例**:
```bash
# 使用默认参数检查
./bin/checkPhotoQueue.sh

# 指定收件人
./bin/checkPhotoQueue.sh "<EMAIL>"

# 指定收件人和阈值
./bin/checkPhotoQueue.sh "<EMAIL>" 40000
```

### 3. setupCrontab.sh (定时任务设置)

**功能**: 自动设置每3小时运行一次的定时任务

**用法**:
```bash
./bin/setupCrontab.sh
```

**功能特性**:
- 交互式设置收件人和阈值
- 自动备份现有crontab
- 检测并处理重复任务
- 设置日志输出路径

**定时规则**: 每3小时执行一次 (00:00, 03:00, 06:00, 09:00, 12:00, 15:00, 18:00, 21:00)

### 4. testPhotoQueueMonitor.sh (功能测试)

**功能**: 测试监控系统的各项功能

**用法**:
```bash
./bin/testPhotoQueueMonitor.sh
```

**测试内容**:
- 依赖工具检查 (mongosh, jq, curl, bc)
- 历史数据文件格式验证
- 邮件内容生成测试
- 模拟数据创建和处理

## 配置说明

### 邮件配置
- **发件人**: <EMAIL>
- **邮件服务**: https://ml1.realmaster.cc/send
- **认证头**: X-RM-Auth: RM

### MongoDB配置
脚本使用的默认连接配置：
```
***********************************************************************************************************************************************************
```

## 快速开始

### 1. 测试配置文件读取
```bash
# 验证配置文件读取功能
./bin/testConfigReading.sh
```

### 2. 测试系统功能
```bash
# 检查依赖和测试功能
./bin/testPhotoQueueMonitor.sh
```

### 3. 手动运行一次
```bash
# 使用配置文件中的设置（推荐）
./bin/checkPhotoQueue.sh

# 或指定收件人（覆盖配置文件）
./bin/checkPhotoQueue.sh "<EMAIL>"

# 指定收件人和阈值
./bin/checkPhotoQueue.sh "<EMAIL>" 40000
```

### 4. 设置定时任务 (每3小时运行)
```bash
# 交互式设置（会读取配置文件中的默认邮件地址）
./bin/setupCrontab.sh
```

### 5. 监控运行状态
```bash
# 查看定时任务日志
tail -f /tmp/photo_queue_monitor.log

# 查看历史数据
cat logs/photo_queue_history.json | jq .

# 查看当前定时任务
crontab -l
```

## 定时任务详情

**自动设置** (推荐):
```bash
./bin/setupCrontab.sh
```

**手动设置**:
```bash
# 每3小时检查一次照片队列
0 */3 * * * /path/to/rmconfig/bin/checkPhotoQueue.sh >> /tmp/photo_queue_monitor.log 2>&1

# 自定义收件人和阈值
0 */3 * * * /path/to/rmconfig/bin/checkPhotoQueue.sh "<EMAIL>" 40000 >> /tmp/photo_queue_monitor.log 2>&1
```

## 依赖要求

确保系统已安装以下工具：
- `mongosh` - MongoDB Shell
- `jq` - JSON处理工具
- `curl` - HTTP客户端
- `bc` - 计算器 (用于百分比计算)

## 邮件格式

邮件采用HTML格式，包含：
- 查询时间
- 查询条件
- 结果统计
- 告警信息 (如适用)
- 建议操作 (告警时)

## 错误处理

脚本包含以下错误处理：
- MongoDB连接失败检测
- 查询执行失败检测
- 邮件发送失败检测
- 参数验证

## 扩展使用

### 自定义查询示例

```bash
# 查询特定状态的任务
./bin/mongoQueryAndEmail.sh "<EMAIL>" "" "{status:'failed'}" "失败任务报告"

# 查询最近24小时的任务
./bin/mongoQueryAndEmail.sh "<EMAIL>" "" "{createdAt:{\$gte:new Date(Date.now()-24*60*60*1000)}}" "24小时任务报告"
```

### 修改告警阈值

编辑 `checkPhotoQueue.sh` 文件中的以下行：
```bash
# 修改高优先级任务告警阈值
if [ "$HIGH_PRIORITY_COUNT" -gt 100 ]; then

# 修改总任务数量告警阈值  
if [ "$TOTAL_COUNT" -gt 500000 ]; then
```

## 注意事项

1. 确保MongoDB连接字符串正确且有访问权限
2. 邮件服务需要正确的认证配置
3. 建议在测试环境先验证脚本功能
4. 定时任务的频率要合理，避免过于频繁的查询
5. 监控脚本的执行日志，及时发现问题
