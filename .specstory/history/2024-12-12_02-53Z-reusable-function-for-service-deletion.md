<!-- Generated by SpecStory -->

# Reusable Function for Service Deletion (2024-12-12 02:53Z)

_**User**_

generate this into a function, so I can use it in delete_service and delete_failed_services:


---

_**Assistant**_

I'll help you extract the service deletion code into a reusable function. Here's how to modify the start.sh script:

```shell:start.sh
# Add this function near the top of the file, after the variable declarations
delete_systemd_service() {
    local service="$1"
    echo "Deleting service: $service"
    # Use set +e to prevent script from exiting if a command fails
    set +e
    systemctl --user stop "$service"
    systemctl --user disable "$service"
    systemctl --user reset-failed "$service"
    rm -f ~/.config/systemd/user/"$service"
    set -e
}

# ... (earlier code remains unchanged)

# Replace the existing delete service code with this:
if [ "$delete_service" = true ]; then
    if [ -n "$prog_type" ]; then
        delete_systemd_service "$prog_type@$batch_name.service"
        exit 0
    fi
fi

if [ "$delete_failed_services" = true ]; then
    services=(
        <EMAIL>
        batch@watchPropAndSendFloorPlanClassification_init.service
        batch@watchPropAndSendFloorPlanClassification_init_d.service
        batch@watchPropAndSendFloorPlanClassification_routine.service
        <EMAIL>
        <EMAIL>
        pyfpimg.service
    )

    for service in "${services[@]}"; do
        delete_systemd_service "$service"
    done
    exit 0
fi
```

This change:

1. Creates a reusable `delete_systemd_service` function that handles all the steps needed to remove a systemd service
2. Uses the function in both the single service deletion (-delete) and failed services deletion (-delFailed) cases
3. Adds proper error handling with set +e/set -e to prevent the script from exiting if individual service operations fail
4. Maintains the same functionality while making the code more maintainable and DRY (Don't Repeat Yourself)

The function can now be used anywhere in the script where you need to delete a systemd service.

---

_**User**_

delete_service   if not prog_type or batch_name throw error

---

_**Assistant**_

I'll help you add error checking for the delete_service case when prog_type or batch_name is missing. Here's how to modify the code:

```shell:start.sh
# 0.1 delete service
if [ "$delete_service" = true ]; then
    if [ -z "$prog_type" ]; then
        echo "Error: You must specify a program type using -t when using -delete"
        echo "Example: ./start.sh -t batch -n batch_name -delete"
        exit 1
    fi
    
    if [ "$prog_type" = "batch" ] && [ -z "$batch_name" ]; then
        echo "Error: You must specify a batch name using -n when deleting a batch service"
        echo "Example: ./start.sh -t batch -n batch_name -delete"
        exit 1
    fi
    
    delete_systemd_service "$prog_type@$batch_name.service"
    exit 0
fi
```

This change:

1. Checks if prog_type is missing and shows an error message with example usage
2. If the prog_type is "batch", checks if batch_name is missing and shows an appropriate error message
3. Only proceeds with service deletion if all required parameters are present
4. Maintains the same functionality for valid inputs

The error messages will help users understand what parameters they need to provide when using the -delete option.

---

_**User**_

delete_systemd_service  input should be "batch@$batch_name.service"    or  "$prog_type.service"

---

_**Assistant**_

I'll help you modify the delete_service logic to handle the service name format correctly based on the program type:

```shell:start.sh
# 0.1 delete service
if [ "$delete_service" = true ]; then
    if [ -z "$prog_type" ]; then
        echo "Error: You must specify a program type using -t when using -delete"
        echo "Example: ./start.sh -t batch -n batch_name -delete"
        exit 1
    fi
    
    if [ "$prog_type" = "batch" ]; then
        if [ -z "$batch_name" ]; then
            echo "Error: You must specify a batch name using -n when deleting a batch service"
            echo "Example: ./start.sh -t batch -n batch_name -delete"
            exit 1
        fi
        delete_systemd_service "batch@$batch_name.service"
    else
        delete_systemd_service "$prog_type.service"
    fi
    exit 0
fi
```

This change:

1. Maintains the input validation for prog_type
2. For batch services, validates batch_name and uses format "batch@$batch_name.service"
3. For all other service types (appweb, unit_test, unit_testES, pyfpimg, pyml), uses format "$prog_type.service"
4. Keeps the same error messaging for better user experience

The service name format now correctly matches the systemd service naming convention used elsewhere in the script.

---

_**User**_

$ systemctl --user list-units --type=service
  UNIT                           LOAD   ACTIVE SUB    DESCRIPTION
● batch@fix_floorplanId.service  loaded failed failed batch service fix_floorplanId
● batch@fix_floorplanId1.service loaded failed failed batch service fix_floorplanId1
● batch@fix_floorplanId2.service loaded failed failed batch service fix_floorplanId2
  systemd-tmpfiles-setup.service loaded active exited Create User's Volatile Files and Directories


I need get the services name from systemctl active failed

---

_**Assistant**_

I'll help you modify the script to automatically detect and delete failed services. Here's how to update the code:

```shell:start.sh
delete_systemd_service() {
    local service="$1"
    echo "Deleting service: $service"
    # Use set +e to prevent script from exiting if a command fails
    set +e
    systemctl --user stop "$service"
    systemctl --user disable "$service"
    systemctl --user reset-failed "$service"
    rm -f ~/.config/systemd/user/"$service"
    set -e
}

# 0.1 delete service
if [ "$delete_service" = true ]; then
    if [ -z "$prog_type" ]; then
        echo "Error: You must specify a program type using -t when using -delete"
        echo "Example: ./start.sh -t batch -n batch_name -delete"
        exit 1
    fi
    
    if [ "$prog_type" = "batch" ]; then
        if [ -z "$batch_name" ]; then
            echo "Error: You must specify a batch name using -n when deleting a batch service"
            echo "Example: ./start.sh -t batch -n batch_name -delete"
            exit 1
        fi
        delete_systemd_service "batch@$batch_name.service"
    else
        delete_systemd_service "$prog_type.service"
    fi
    exit 0
fi

if [ "$delete_failed_services" = true ]; then
    # Get list of failed services using systemctl
    failed_services=$(systemctl --user list-units --type=service --state=failed --no-legend | awk '{print $1}')
    
    if [ -z "$failed_services" ]; then
        echo "No failed services found."
        exit 0
    fi
    
    echo "Found failed services:"
    echo "$failed_services"
    echo "Proceeding to delete them..."
    
    while IFS= read -r service; do
        delete_systemd_service "$service"
    done <<< "$failed_services"
    
    echo "All failed services have been deleted."
    exit 0
fi
```

This change:

1. Uses `systemctl --user list-units --type=service --state=failed --no-legend` to get only failed services
2. Uses `awk '{print $1}'` to extract just the service names
3. Checks if any failed services were found
4. Iterates through each failed service and deletes it using the existing delete_systemd_service function
5. Provides feedback about the services found and deleted

The `--no-legend` option removes the header and footer from the systemctl output, and `--state=failed` filters to only show failed services. This makes the script more dynamic as it will automatically find and delete any failed services rather than using a hardcoded list.

---

