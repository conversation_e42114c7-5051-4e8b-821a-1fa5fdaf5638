#!/bin/bash

# MongoDB查询并发送邮件脚本
# 用法: ./mongoQueryAndEmail.sh [收件人邮箱] [MongoDB连接URI] [查询条件] [邮件主题]

# 默认配置
RM_FROM="<EMAIL>"
RM_URL="https://ml1.realmaster.cc/send"

# 脚本目录和配置文件
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_CONFIG_FILE="$SCRIPT_DIR/../local.ini"

# 参数处理
RM_TO="$1"
MONGO_URI="$2"
QUERY_CONDITION="$3"
EMAIL_SUBJECT="$4"

# 从配置文件读取默认值
echo "正在从配置文件读取设置..."

# 默认值设置 - 邮件地址
if [ -z "$RM_TO" ]; then
    if [ -f "$LOCAL_CONFIG_FILE" ]; then
        CONFIG_EMAIL=$(coffee "$SCRIPT_DIR/getValueFromToml.coffee" "$LOCAL_CONFIG_FILE" "contact.alertEmail" 2>/dev/null)
        if [ -n "$CONFIG_EMAIL" ]; then
            RM_TO="$CONFIG_EMAIL"
            echo "从配置文件读取收件人: $RM_TO"
        else
            RM_TO="<EMAIL>,<EMAIL>"
            echo "配置文件中未找到邮件地址，使用默认收件人: $RM_TO"
        fi
    else
        RM_TO="<EMAIL>,<EMAIL>"
        echo "配置文件不存在，使用默认收件人: $RM_TO"
    fi
else
    echo "使用命令行指定的收件人: $RM_TO"
fi

# 默认值设置 - MongoDB连接
if [ -z "$MONGO_URI" ]; then
    if [ -f "$LOCAL_CONFIG_FILE" ]; then
        CONFIG_MONGO_URI=$(coffee "$SCRIPT_DIR/getValueFromToml.coffee" "$LOCAL_CONFIG_FILE" "dbs.rni.uri" 2>/dev/null)
        if [ -n "$CONFIG_MONGO_URI" ]; then
            MONGO_URI="$CONFIG_MONGO_URI"
            echo "从配置文件读取MongoDB连接"
        else
            MONGO_URI="***********************************************************************************************************************************************************"
            echo "配置文件中未找到MongoDB连接，使用默认连接"
        fi
    else
        MONGO_URI="***********************************************************************************************************************************************************"
        echo "配置文件不存在，使用默认MongoDB连接"
    fi
else
    echo "使用命令行指定的MongoDB连接"
fi

if [ -z "$QUERY_CONDITION" ]; then
    QUERY_CONDITION="{priority:{\$gt:35000}}"
    echo "使用默认查询条件: $QUERY_CONDITION"
fi

if [ -z "$EMAIL_SUBJECT" ]; then
    EMAIL_SUBJECT="MongoDB查询结果报告"
fi

echo "==================== MongoDB查询开始 ===================="
echo "MongoDB URI: $MONGO_URI"
echo "查询条件: $QUERY_CONDITION"
echo "收件人: $RM_TO"
echo "邮件主题: $EMAIL_SUBJECT"

# 执行MongoDB查询
echo "正在执行MongoDB查询..."

# 查询高优先级任务数量
HIGH_PRIORITY_COUNT=$(mongosh "$MONGO_URI" --quiet --eval "
db.reso_photo_download_queue.find($QUERY_CONDITION).count()
")

# 查询总任务数量
TOTAL_COUNT=$(mongosh "$MONGO_URI" --quiet --eval "
db.reso_photo_download_queue.find().count()
")

# 检查查询是否成功
if [ $? -ne 0 ]; then
    echo "MongoDB查询失败!"
    exit 1
fi

echo "查询结果:"
echo "高优先级任务数量: $HIGH_PRIORITY_COUNT"
echo "总任务数量: $TOTAL_COUNT"

# 计算百分比
if [ "$TOTAL_COUNT" -gt 0 ]; then
    PERCENTAGE=$(echo "scale=2; $HIGH_PRIORITY_COUNT * 100 / $TOTAL_COUNT" | bc)
else
    PERCENTAGE="0"
fi

# 生成邮件内容
CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')
rmText="<h2>MongoDB查询结果报告</h2>
<p><strong>查询时间:</strong> $CURRENT_TIME</p>
<p><strong>数据库:</strong> reso_photo_download_queue</p>
<p><strong>查询条件:</strong> $QUERY_CONDITION</p>
<hr>
<h3>查询结果:</h3>
<ul>
<li><strong>高优先级任务数量:</strong> $HIGH_PRIORITY_COUNT</li>
<li><strong>总任务数量:</strong> $TOTAL_COUNT</li>
<li><strong>高优先级任务占比:</strong> $PERCENTAGE%</li>
</ul>
<hr>
<p><em>此邮件由自动化脚本生成</em></p>"

echo "邮件内容: $rmText"
echo "收件人: $RM_TO"

# 检查是否需要发送邮件
if [ ${#rmText} -gt 0 ] && [ ${#RM_TO} -gt 0 ]; then
  echo "======================= 发送邮件 ======================="
  
  # 构建邮件JSON
  mail=$(jq -n \
    --arg from "$RM_FROM" \
    --arg to "$RM_TO" \
    --arg subject "$EMAIL_SUBJECT" \
    --arg html "$rmText" \
    '{from: $from, to: $to, subject: $subject, html: $html}')
  
  # 将收件人字符串转换为数组
  newEmail=$(jq -r '.to |= split(",")' <<< "${mail}")
  
  echo "发送邮件到: $RM_TO"
  
  # 发送邮件
  response=$(curl -X POST \
    --header "Content-Type: application/json" \
    --header "X-RM-Auth: RM" \
    -s \
    --data "$newEmail" \
    $RM_URL)
  
  if [ $? -eq 0 ]; then
    echo "邮件发送成功!"
    echo "响应: $response"
  else
    echo "邮件发送失败!"
    exit 1
  fi
else
  echo "邮件内容或收件人为空，跳过发送邮件"
fi

echo "======================= 脚本执行完成 ======================="
exit 0
