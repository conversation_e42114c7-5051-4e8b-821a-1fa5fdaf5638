#!/bin/bash

# === 配置你的ES地址 ===
ES_HOST="https://************:9200"
SCRIPT_ID="search_by_wildcard"

# === 构造JSON body ===
SCRIPT_BODY=$(cat <<EOF
{
  "script": {
    "lang": "painless",
    "source": "def field = params.field; def val = doc.containsKey(field) && doc[field].size() > 0 ? doc[field].value.toLowerCase() : \"\"; def score = 0; def origScore = doc.containsKey('penalty') && !doc['penalty'].empty ? doc['penalty'].value : 0; if (field == \"searchAddr\") { def prefix = params.prefix.toLowerCase(); def tokens = params.tokens; if (val.startsWith(prefix)) { score += 100; } else { for (token in tokens) { if (val.contains(token)) { score += 10; } } } score += origScore; } else { score = 100 - val.length(); } return 1000 - score;"
  }
}
EOF
)

# === 上传 Script ===
echo "Uploading stored script [$SCRIPT_ID] to Elasticsearch at $ES_HOST"
curl --insecure -u "elastic:NomtIKGKox14ml96XX7T" \
  -X PUT "$ES_HOST/_scripts/$SCRIPT_ID" \
  -H 'Content-Type: application/json' \
  -d "$SCRIPT_BODY"
