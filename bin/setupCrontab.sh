#!/bin/bash

# 设置照片队列监控的定时任务脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MONITOR_SCRIPT="$SCRIPT_DIR/checkPhotoQueue.sh"

echo "==================== 设置定时任务 ===================="
echo "监控脚本路径: $MONITOR_SCRIPT"

# 检查脚本是否存在
if [ ! -f "$MONITOR_SCRIPT" ]; then
    echo "错误: 监控脚本不存在: $MONITOR_SCRIPT"
    exit 1
fi

# 确保脚本有执行权限
chmod +x "$MONITOR_SCRIPT"

# 获取当前用户的crontab
echo "正在备份当前的crontab..."
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "当前没有crontab任务"

# 检查是否已经存在相同的任务
EXISTING_TASK=$(crontab -l 2>/dev/null | grep "checkPhotoQueue.sh" || echo "")

if [ -n "$EXISTING_TASK" ]; then
    echo "发现已存在的照片队列监控任务:"
    echo "$EXISTING_TASK"
    echo ""
    read -p "是否要替换现有任务? (y/N): " REPLACE
    
    if [ "$REPLACE" = "y" ] || [ "$REPLACE" = "Y" ]; then
        echo "正在移除现有任务..."
        # 移除现有的checkPhotoQueue.sh任务
        crontab -l 2>/dev/null | grep -v "checkPhotoQueue.sh" | crontab -
    else
        echo "保留现有任务，退出设置"
        exit 0
    fi
fi

# 从配置文件读取默认邮件地址
LOCAL_CONFIG_FILE="$SCRIPT_DIR/../local.ini"
DEFAULT_EMAIL=""

if [ -f "$LOCAL_CONFIG_FILE" ]; then
    DEFAULT_EMAIL=$(coffee "$SCRIPT_DIR/getValueFromToml.coffee" "$LOCAL_CONFIG_FILE" "contact.alertEmail" 2>/dev/null)
    if [ -n "$DEFAULT_EMAIL" ]; then
        echo "从配置文件读取到默认邮件地址: $DEFAULT_EMAIL"
    fi
fi

# 询问收件人邮箱
echo ""
if [ -n "$DEFAULT_EMAIL" ]; then
    read -p "请输入收件人邮箱 (多个邮箱用逗号分隔，回车使用配置文件中的 $DEFAULT_EMAIL): " EMAIL_TO
else
    read -p "请输入收件人邮箱 (多个邮箱用逗号分隔，回车使用脚本默认值): " EMAIL_TO
fi

if [ -z "$EMAIL_TO" ]; then
    if [ -n "$DEFAULT_EMAIL" ]; then
        EMAIL_TO="$DEFAULT_EMAIL"
        echo "使用配置文件中的邮件地址: $EMAIL_TO"
    fi
    CRON_COMMAND="$MONITOR_SCRIPT"
else
    CRON_COMMAND="$MONITOR_SCRIPT \"$EMAIL_TO\""
fi

# 询问优先级阈值
echo ""
read -p "请输入优先级阈值 (回车使用默认35000): " THRESHOLD

if [ -n "$THRESHOLD" ]; then
    CRON_COMMAND="$CRON_COMMAND $THRESHOLD"
fi

# 创建新的crontab条目
# 每3小时运行一次 (0 */3 * * *)
NEW_CRON_ENTRY="0 */3 * * * $CRON_COMMAND >> /tmp/photo_queue_monitor.log 2>&1"

echo ""
echo "将要添加的定时任务:"
echo "$NEW_CRON_ENTRY"
echo ""
echo "任务说明:"
echo "- 每3小时执行一次 (00:00, 03:00, 06:00, 09:00, 12:00, 15:00, 18:00, 21:00)"
echo "- 日志输出到: /tmp/photo_queue_monitor.log"
echo "- 监控脚本: $MONITOR_SCRIPT"
if [ -n "$EMAIL_TO" ]; then
    echo "- 收件人: $EMAIL_TO"
fi
if [ -n "$THRESHOLD" ]; then
    echo "- 优先级阈值: $THRESHOLD"
fi

echo ""
read -p "确认添加此定时任务? (y/N): " CONFIRM

if [ "$CONFIRM" = "y" ] || [ "$CONFIRM" = "Y" ]; then
    # 添加新任务到crontab
    (crontab -l 2>/dev/null; echo "$NEW_CRON_ENTRY") | crontab -
    
    if [ $? -eq 0 ]; then
        echo "✅ 定时任务设置成功!"
        echo ""
        echo "当前的crontab任务:"
        crontab -l
        echo ""
        echo "📝 管理提示:"
        echo "- 查看日志: tail -f /tmp/photo_queue_monitor.log"
        echo "- 手动运行: $MONITOR_SCRIPT"
        echo "- 移除任务: crontab -e (然后删除相应行)"
        echo "- 查看任务: crontab -l"
        echo ""
        echo "🔔 首次运行:"
        echo "定时任务将在下一个整点的3小时倍数时间运行"
        echo "你也可以现在手动运行一次测试: $MONITOR_SCRIPT"
    else
        echo "❌ 定时任务设置失败!"
        exit 1
    fi
else
    echo "取消设置定时任务"
fi

echo ""
echo "==================== 设置完成 ===================="
