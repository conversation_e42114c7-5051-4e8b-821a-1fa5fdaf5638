# MongoDB查询邮件脚本使用说明

## 脚本概述

创建了两个MongoDB查询并发送邮件的shell脚本：

1. `mongoQueryAndEmail.sh` - 通用的MongoDB查询邮件脚本
2. `checkPhotoQueue.sh` - 专门用于检查照片下载队列的监控脚本

## 脚本功能

### 1. mongoQueryAndEmail.sh (通用脚本)

**功能**: 执行自定义MongoDB查询并将结果通过邮件发送

**用法**:
```bash
./bin/mongoQueryAndEmail.sh [收件人邮箱] [MongoDB连接URI] [查询条件] [邮件主题]
```

**参数说明**:
- `收件人邮箱`: 邮件接收者，多个邮箱用逗号分隔 (默认: <EMAIL>,<EMAIL>)
- `MongoDB连接URI`: 数据库连接字符串 (默认: RNI测试数据库)
- `查询条件`: MongoDB查询条件，JSON格式 (默认: {priority:{$gt:35000}})
- `邮件主题`: 邮件标题 (默认: "MongoDB查询结果报告")

**示例**:
```bash
# 使用默认参数
./bin/mongoQueryAndEmail.sh

# 指定收件人
./bin/mongoQueryAndEmail.sh "<EMAIL>"

# 完整参数
./bin/mongoQueryAndEmail.sh "<EMAIL>" "mongodb://..." "{status:'pending'}" "待处理任务报告"
```

### 2. checkPhotoQueue.sh (专用监控脚本)

**功能**: 监控照片下载队列状态，当异常时发送告警邮件

**用法**:
```bash
./bin/checkPhotoQueue.sh [收件人邮箱] [优先级阈值]
```

**参数说明**:
- `收件人邮箱`: 邮件接收者 (默认: <EMAIL>,<EMAIL>)
- `优先级阈值`: 高优先级任务的阈值 (默认: 35000)

**告警条件**:
- 高优先级任务数量 > 100
- 总任务数量 > 500000

**示例**:
```bash
# 使用默认参数检查
./bin/checkPhotoQueue.sh

# 指定收件人
./bin/checkPhotoQueue.sh "<EMAIL>"

# 指定收件人和阈值
./bin/checkPhotoQueue.sh "<EMAIL>" 40000
```

## 配置说明

### 邮件配置
- **发件人**: <EMAIL>
- **邮件服务**: https://ml1.realmaster.cc/send
- **认证头**: X-RM-Auth: RM

### MongoDB配置
脚本使用的默认连接配置：
```
***********************************************************************************************************************************************************
```

## 定时任务设置

可以通过crontab设置定时执行：

```bash
# 每小时检查一次照片队列
0 * * * * /path/to/rmconfig/bin/checkPhotoQueue.sh

# 每天早上8点发送队列状态报告
0 8 * * * /path/to/rmconfig/bin/mongoQueryAndEmail.sh "<EMAIL>" "" "" "每日队列状态报告"
```

## 依赖要求

确保系统已安装以下工具：
- `mongosh` - MongoDB Shell
- `jq` - JSON处理工具
- `curl` - HTTP客户端
- `bc` - 计算器 (用于百分比计算)

## 邮件格式

邮件采用HTML格式，包含：
- 查询时间
- 查询条件
- 结果统计
- 告警信息 (如适用)
- 建议操作 (告警时)

## 错误处理

脚本包含以下错误处理：
- MongoDB连接失败检测
- 查询执行失败检测
- 邮件发送失败检测
- 参数验证

## 扩展使用

### 自定义查询示例

```bash
# 查询特定状态的任务
./bin/mongoQueryAndEmail.sh "<EMAIL>" "" "{status:'failed'}" "失败任务报告"

# 查询最近24小时的任务
./bin/mongoQueryAndEmail.sh "<EMAIL>" "" "{createdAt:{\$gte:new Date(Date.now()-24*60*60*1000)}}" "24小时任务报告"
```

### 修改告警阈值

编辑 `checkPhotoQueue.sh` 文件中的以下行：
```bash
# 修改高优先级任务告警阈值
if [ "$HIGH_PRIORITY_COUNT" -gt 100 ]; then

# 修改总任务数量告警阈值  
if [ "$TOTAL_COUNT" -gt 500000 ]; then
```

## 注意事项

1. 确保MongoDB连接字符串正确且有访问权限
2. 邮件服务需要正确的认证配置
3. 建议在测试环境先验证脚本功能
4. 定时任务的频率要合理，避免过于频繁的查询
5. 监控脚本的执行日志，及时发现问题
