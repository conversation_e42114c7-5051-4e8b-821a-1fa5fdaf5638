# MongoDB照片队列监控

## 功能
- 监控 `reso_photo_download_queue` 集合中的高优先级任务
- 自动从 `local.ini` 读取邮件地址和数据库连接
- 保存历史数据（最近10次检查）并在邮件中显示趋势
- 智能告警：高优先级任务>100 或 总任务>500000 时发送告警

## 使用方法

### 手动运行
```bash
# 使用配置文件中的设置
./bin/checkPhotoQueue.sh

# 指定收件人
./bin/checkPhotoQueue.sh "<EMAIL>"

# 指定收件人和阈值
./bin/checkPhotoQueue.sh "<EMAIL>" 40000
```

### 设置定时任务（每3小时运行）
```bash
# 添加到crontab
crontab -e

# 添加以下行：
0 */3 * * * /path/to/rmconfig/bin/checkPhotoQueue.sh >> /tmp/photo_queue_monitor.log 2>&1
```

### 查看日志和历史数据
```bash
# 查看运行日志
tail -f /tmp/photo_queue_monitor.log

# 查看历史数据
cat logs/photo_queue_history.json | jq .
```

## 配置
脚本自动从 `local.ini` 读取：
- 邮件地址：`contact.alertEmail`
- MongoDB连接：`dbs.rni.uri`

## 文件说明
- `checkPhotoQueue.sh` - 主监控脚本
- `getValueFromToml.coffee` - 配置文件读取工具（已存在）
- `logs/photo_queue_history.json` - 历史数据文件（自动创建）
