<!-- Generated by SpecStory -->

# Parameter Validation Function Implementation (2024-12-12 02:53Z)

_**User**_

check if prog_type and batch_name, if not exitst error.  also wrtie a function, so I can use it also in delete_failed_services

---

_**Assistant**_

I'll help you create a function to check for required parameters and show how to use it in both contexts. Here's the solution:

```python:utils/validation.py
def validate_required_params(prog_type: str = None, batch_name: str = None) -> tuple[str, str]:
    """
    Validates required parameters prog_type and batch_name.
    
    Args:
        prog_type (str): Program type parameter
        batch_name (str): Batch name parameter
        
    Returns:
        tuple[str, str]: Validated prog_type and batch_name
        
    Raises:
        ValueError: If either parameter is missing or empty
    """
    if not prog_type or not batch_name:
        raise ValueError("Both prog_type and batch_name are required parameters")
    
    prog_type = prog_type.strip()
    batch_name = batch_name.strip()
    
    if not prog_type or not batch_name:
        raise ValueError("prog_type and batch_name cannot be empty strings")
        
    return prog_type, batch_name
```

Then you can use this function in your scripts like this:

```python:your_script.py
from utils.validation import validate_required_params

# In your main script
try:
    prog_type, batch_name = validate_required_params(prog_type, batch_name)
    # Continue with your logic
except ValueError as e:
    print(f"Error: {e}")
    sys.exit(1)
```

```python:delete_failed_services.py
from utils.validation import validate_required_params

# In delete_failed_services
try:
    prog_type, batch_name = validate_required_params(prog_type, batch_name)
    # Continue with service deletion logic
except ValueError as e:
    print(f"Error: {e}")
    sys.exit(1)
```

This solution:
1. Creates a reusable validation function
2. Handles both None values and empty strings
3. Returns cleaned (stripped) values
4. Raises a descriptive ValueError if validation fails
5. Can be used consistently across different scripts
6. Includes type hints for better code documentation

The error handling with try/except blocks ensures a clean exit with an error message if the validation fails.

---

_**User**_

@start.sh    add #0.1 herer

---

_**Assistant**_

I'll help add the validation for the delete service section. Here's the updated code:

```bash:start.sh
# 0.1 delete service
if [ "$delete_service" = true ]; then
  # Validate required parameters for service deletion
  if [ -z "$prog_type" ] || [ -z "$batch_name" ]; then
    echo "Error: Both prog_type (-t) and batch_name (-n) are required for service deletion"
    exit 1
  fi

  echo "Deleting service $prog_type@$batch_name"
  systemctl --user stop $prog_type@$batch_name.service
  systemctl --user disable $prog_type@$batch_name.service
  systemctl --user reset-failed $prog_type@$batch_name.service
  rm -f ~/.config/systemd/user/$prog_type@$batch_name.service
  exit 0
fi

# Handle delete failed services
if [ "$delete_failed_services" = true ]; then
  # List of services to clean up
  services=(
    <EMAIL>
    batch@watchPropAndSendFloorPlanClassification_init.service
    batch@watchPropAndSendFloorPlanClassification_init_d.service
    batch@watchPropAndSendFloorPlanClassification_routine.service
    <EMAIL>
    <EMAIL>
    pyfpimg.service
  )

  echo "Cleaning up failed services..."
  for service in "${services[@]}"; do
    echo "Processing $service..."
    systemctl --user stop "$service"
    systemctl --user disable "$service"
    systemctl --user reset-failed "$service"
    rm -f ~/.config/systemd/user/"$service"
  done
  
  echo "All failed services have been cleaned up"
  exit 0
fi
```

This update:
1. Adds proper validation for required parameters when deleting a specific service
2. Provides clear error messages if parameters are missing
3. Maintains the existing functionality for both single service deletion and failed services cleanup
4. Adds status messages to make the process more transparent
5. Ensures clean exit after operations are complete

The code will now properly validate parameters before attempting to delete services and provide better feedback to users.

---

