# MongoDB照片队列监控系统 - 设置完成总结

## ✅ 已完成的功能

### 1. 配置文件集成
- ✅ 自动从 `local.ini` 读取邮件地址：`contact.alertEmail = '<EMAIL>'`
- ✅ 自动从 `local.ini` 读取MongoDB连接：`dbs.rni.uri = "mongodb://..."`
- ✅ 配置优先级：命令行参数 > 配置文件 > 默认值

### 2. 增强的监控脚本
- ✅ `checkPhotoQueue.sh` - 包含历史数据对比的照片队列监控
- ✅ 自动保存最近10次检查结果
- ✅ 邮件包含趋势分析和统计信息
- ✅ 智能告警机制（高优先级>100 或 总数>500000）

### 3. 定时任务系统
- ✅ `setupCrontab.sh` - 交互式设置每3小时运行的定时任务
- ✅ 自动读取配置文件中的默认邮件地址
- ✅ 日志输出到 `/tmp/photo_queue_monitor.log`

### 4. 测试和验证工具
- ✅ `testConfigReading.sh` - 验证配置文件读取功能
- ✅ `testPhotoQueueMonitor.sh` - 测试监控系统功能
- ✅ 依赖检查和模拟数据测试

## 🚀 立即开始使用

### 第一步：验证配置
```bash
./bin/testConfigReading.sh
```
**结果**: ✅ 成功读取到 `<EMAIL>` 和 MongoDB连接

### 第二步：设置定时任务（每3小时运行）
```bash
./bin/setupCrontab.sh
```
**操作**: 
- 回车使用配置文件中的邮件地址 `<EMAIL>`
- 回车使用默认阈值 35000
- 确认添加定时任务

### 第三步：手动测试一次
```bash
./bin/checkPhotoQueue.sh
```

## 📧 邮件内容示例

邮件将包含：
- **当前状态**: 高优先级任务数量、总任务数量
- **历史趋势表格**: 最近10次检查的对比数据
- **统计信息**: 平均值、最大值、最小值
- **变化趋势**: 📈📉➡️ 图标显示增减趋势
- **告警信息**: 超过阈值时的红色告警

## 📊 监控数据

### 查询条件
- **高优先级任务**: `db.reso_photo_download_queue.find({priority:{$gt:35000}}).count()`
- **总任务数**: `db.reso_photo_download_queue.find().count()`

### 历史数据存储
- **文件位置**: `logs/photo_queue_history.json`
- **保留数量**: 最近10次检查结果
- **数据格式**: JSON，包含时间戳、任务数量、阈值等

## ⏰ 定时任务详情

### 运行时间
每3小时运行一次：00:00, 03:00, 06:00, 09:00, 12:00, 15:00, 18:00, 21:00

### Crontab条目
```bash
0 */3 * * * /home/<USER>/github/rmconfig/bin/checkPhotoQueue.sh >> /tmp/photo_queue_monitor.log 2>&1
```

## 🔧 管理命令

### 查看运行日志
```bash
tail -f /tmp/photo_queue_monitor.log
```

### 查看历史数据
```bash
cat logs/photo_queue_history.json | jq .
```

### 查看当前定时任务
```bash
crontab -l
```

### 手动运行（不同参数）
```bash
# 使用配置文件设置
./bin/checkPhotoQueue.sh

# 指定收件人
./bin/checkPhotoQueue.sh "<EMAIL>"

# 指定收件人和阈值
./bin/checkPhotoQueue.sh "<EMAIL>" 40000
```

## 📁 文件结构

```
bin/
├── checkPhotoQueue.sh          # 主监控脚本（增强版）
├── mongoQueryAndEmail.sh       # 通用MongoDB查询脚本
├── setupCrontab.sh            # 定时任务设置脚本
├── testConfigReading.sh       # 配置读取测试脚本
├── testPhotoQueueMonitor.sh   # 功能测试脚本
├── getValueFromToml.coffee    # 配置文件读取工具
└── README_mongo_email.md      # 详细使用说明

logs/
└── photo_queue_history.json   # 历史数据文件

local.ini                      # 配置文件
```

## 🎯 下一步建议

1. **立即设置定时任务**：运行 `./bin/setupCrontab.sh`
2. **监控首次运行**：查看 `/tmp/photo_queue_monitor.log`
3. **验证邮件接收**：确认能收到监控邮件
4. **调整告警阈值**：根据实际情况修改脚本中的告警条件

## 🔍 故障排除

### 如果收不到邮件
1. 检查邮件服务配置：`RM_URL=https://ml1.realmaster.cc/send`
2. 验证邮件地址格式
3. 查看日志中的错误信息

### 如果MongoDB连接失败
1. 检查网络连接
2. 验证数据库凭据
3. 确认数据库服务状态

### 如果配置读取失败
1. 检查 `local.ini` 文件存在
2. 验证CoffeeScript安装：`npm install -g coffeescript`
3. 检查文件权限

---

**系统已就绪！** 🎉 现在你有了一个完整的MongoDB照片队列监控系统，包含历史数据对比和自动邮件通知功能。
