#!/bin/bash

# 测试配置文件读取功能

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOCAL_CONFIG_FILE="$SCRIPT_DIR/../local.ini"

echo "==================== 测试配置文件读取 ===================="
echo "配置文件路径: $LOCAL_CONFIG_FILE"

# 检查配置文件是否存在
if [ ! -f "$LOCAL_CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $LOCAL_CONFIG_FILE"
    exit 1
fi

echo "✅ 配置文件存在"

# 检查coffee是否可用
if ! command -v coffee >/dev/null 2>&1; then
    echo "❌ CoffeeScript未安装，请安装: npm install -g coffeescript"
    exit 1
fi

echo "✅ CoffeeScript已安装"

# 检查getValueFromToml.coffee是否存在
TOML_READER="$SCRIPT_DIR/getValueFromToml.coffee"
if [ ! -f "$TOML_READER" ]; then
    echo "❌ TOML读取工具不存在: $TOML_READER"
    exit 1
fi

echo "✅ TOML读取工具存在"

echo ""
echo "==================== 读取配置值 ===================="

# 测试读取邮件地址
echo "测试读取 contact.alertEmail..."
ALERT_EMAIL=$(coffee "$TOML_READER" "$LOCAL_CONFIG_FILE" "contact.alertEmail" 2>/dev/null)
if [ -n "$ALERT_EMAIL" ]; then
    echo "✅ contact.alertEmail = '$ALERT_EMAIL'"
else
    echo "❌ 无法读取 contact.alertEmail"
fi

# 测试读取MongoDB连接
echo ""
echo "测试读取 dbs.rni.uri..."
RNI_URI=$(coffee "$TOML_READER" "$LOCAL_CONFIG_FILE" "dbs.rni.uri" 2>/dev/null)
if [ -n "$RNI_URI" ]; then
    echo "✅ dbs.rni.uri = '$RNI_URI'"
    # 提取数据库名称
    DB_NAME=$(echo "$RNI_URI" | sed -n 's/.*\/\([^?]*\).*/\1/p')
    echo "   数据库名称: $DB_NAME"
else
    echo "❌ 无法读取 dbs.rni.uri"
fi

# 测试读取其他相关配置
echo ""
echo "测试读取其他配置..."

# 读取项目邮件
PROJECT_EMAIL=$(coffee "$TOML_READER" "$LOCAL_CONFIG_FILE" "contact.projectFubEmail" 2>/dev/null)
if [ -n "$PROJECT_EMAIL" ]; then
    echo "✅ contact.projectFubEmail = '$PROJECT_EMAIL'"
fi

# 读取信任邮件
TRUSTED_EMAIL=$(coffee "$TOML_READER" "$LOCAL_CONFIG_FILE" "contact.trustedAssignEmail" 2>/dev/null)
if [ -n "$TRUSTED_EMAIL" ]; then
    echo "✅ contact.trustedAssignEmail = '$TRUSTED_EMAIL'"
fi

# 读取导入通知邮件
IMPORT_NOTIFY=$(coffee "$TOML_READER" "$LOCAL_CONFIG_FILE" "importNotify.defaultTo" 2>/dev/null)
if [ -n "$IMPORT_NOTIFY" ]; then
    echo "✅ importNotify.defaultTo = '$IMPORT_NOTIFY'"
fi

echo ""
echo "==================== 测试脚本配置读取 ===================="

# 模拟脚本读取配置的过程
echo "模拟 checkPhotoQueue.sh 的配置读取过程..."

# 读取邮件配置
if [ -n "$ALERT_EMAIL" ]; then
    RM_TO="$ALERT_EMAIL"
    echo "✅ 邮件地址设置为: $RM_TO"
else
    RM_TO="<EMAIL>,<EMAIL>"
    echo "⚠️  使用默认邮件地址: $RM_TO"
fi

# 读取MongoDB配置
if [ -n "$RNI_URI" ]; then
    MONGO_URI="$RNI_URI"
    echo "✅ MongoDB连接设置为: ${MONGO_URI:0:50}..."
else
    MONGO_URI="***********************************************************************************************************************************************************"
    echo "⚠️  使用默认MongoDB连接"
fi

echo ""
echo "==================== 配置验证 ===================="

# 验证邮件格式
if [[ "$RM_TO" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(,[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$ ]]; then
    echo "✅ 邮件地址格式正确"
else
    echo "⚠️  邮件地址格式可能有问题: $RM_TO"
fi

# 验证MongoDB连接格式
if [[ "$MONGO_URI" =~ ^mongodb:// ]]; then
    echo "✅ MongoDB连接格式正确"
    
    # 提取主机和端口
    HOST_PORT=$(echo "$MONGO_URI" | sed -n 's/.*@\([^/]*\)\/.*/\1/p')
    if [ -n "$HOST_PORT" ]; then
        echo "   主机:端口 = $HOST_PORT"
    fi
else
    echo "❌ MongoDB连接格式错误"
fi

echo ""
echo "==================== 测试完成 ===================="
echo ""
echo "📋 下一步操作:"
echo "1. 如果配置读取正常，可以运行监控脚本:"
echo "   ./bin/checkPhotoQueue.sh"
echo ""
echo "2. 设置定时任务:"
echo "   ./bin/setupCrontab.sh"
echo ""
echo "3. 查看完整配置文件:"
echo "   cat $LOCAL_CONFIG_FILE"
