#!/bin/bash

# 测试照片队列监控脚本功能

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MONITOR_SCRIPT="$SCRIPT_DIR/checkPhotoQueue.sh"
HISTORY_FILE="$SCRIPT_DIR/../logs/photo_queue_history.json"

echo "==================== 测试照片队列监控 ===================="
echo "监控脚本: $MONITOR_SCRIPT"
echo "历史文件: $HISTORY_FILE"

# 检查依赖
echo ""
echo "检查依赖工具..."

# 检查mongosh
if command -v mongosh >/dev/null 2>&1; then
    echo "✅ mongosh: $(mongosh --version)"
else
    echo "❌ mongosh: 未安装"
    echo "请安装MongoDB Shell: https://docs.mongodb.com/mongodb-shell/install/"
fi

# 检查jq
if command -v jq >/dev/null 2>&1; then
    echo "✅ jq: $(jq --version)"
else
    echo "❌ jq: 未安装"
    echo "请安装jq: sudo apt-get install jq 或 brew install jq"
fi

# 检查curl
if command -v curl >/dev/null 2>&1; then
    echo "✅ curl: $(curl --version | head -n1)"
else
    echo "❌ curl: 未安装"
fi

# 检查bc
if command -v bc >/dev/null 2>&1; then
    echo "✅ bc: 已安装"
else
    echo "❌ bc: 未安装"
    echo "请安装bc: sudo apt-get install bc"
fi

echo ""
echo "==================== 功能测试 ===================="

# 测试1: 创建模拟历史数据
echo "测试1: 创建模拟历史数据..."

# 确保日志目录存在
mkdir -p "$(dirname "$HISTORY_FILE")"

# 创建模拟历史数据
MOCK_HISTORY='[
  {
    "timestamp": '$(date -d "9 hours ago" +%s)',
    "datetime": "'$(date -d "9 hours ago" '+%Y-%m-%d %H:%M:%S')'",
    "high_priority": 45,
    "total": 421000,
    "threshold": 35000
  },
  {
    "timestamp": '$(date -d "6 hours ago" +%s)',
    "datetime": "'$(date -d "6 hours ago" '+%Y-%m-%d %H:%M:%S')'",
    "high_priority": 52,
    "total": 421100,
    "threshold": 35000
  },
  {
    "timestamp": '$(date -d "3 hours ago" +%s)',
    "datetime": "'$(date -d "3 hours ago" '+%Y-%m-%d %H:%M:%S')'",
    "high_priority": 48,
    "total": 421050,
    "threshold": 35000
  }
]'

echo "$MOCK_HISTORY" > "$HISTORY_FILE"
echo "✅ 模拟历史数据已创建"

# 测试2: 检查历史文件格式
echo ""
echo "测试2: 验证历史文件格式..."
if jq empty "$HISTORY_FILE" 2>/dev/null; then
    echo "✅ 历史文件JSON格式正确"
    echo "历史记录数量: $(jq 'length' "$HISTORY_FILE")"
else
    echo "❌ 历史文件JSON格式错误"
fi

# 测试3: 测试邮件内容生成（不发送）
echo ""
echo "测试3: 测试邮件内容生成..."

# 创建测试版本的监控脚本（不连接数据库，不发送邮件）
TEST_SCRIPT="/tmp/test_checkPhotoQueue.sh"
cp "$MONITOR_SCRIPT" "$TEST_SCRIPT"

# 修改测试脚本，使用模拟数据
sed -i 's/mongosh.*count()/echo "430"/' "$TEST_SCRIPT"
sed -i 's/mongosh.*find().count()/echo "421158"/' "$TEST_SCRIPT"
sed -i 's/curl -X POST/echo "模拟发送邮件:" \&\& echo/' "$TEST_SCRIPT"

echo "运行测试脚本..."
chmod +x "$TEST_SCRIPT"

# 运行测试（使用测试邮箱）
"$TEST_SCRIPT" "<EMAIL>" 35000

echo ""
echo "测试4: 检查生成的历史数据..."
if [ -f "$HISTORY_FILE" ]; then
    echo "历史记录数量: $(jq 'length' "$HISTORY_FILE")"
    echo "最新记录时间: $(jq -r '.[-1].datetime' "$HISTORY_FILE")"
    echo "最新高优先级任务数: $(jq -r '.[-1].high_priority' "$HISTORY_FILE")"
else
    echo "❌ 历史文件未生成"
fi

# 清理测试文件
rm -f "$TEST_SCRIPT"

echo ""
echo "==================== 测试完成 ===================="
echo ""
echo "📋 下一步操作:"
echo "1. 如果所有依赖都已安装，可以运行真实测试:"
echo "   $MONITOR_SCRIPT <EMAIL>"
echo ""
echo "2. 设置定时任务:"
echo "   $SCRIPT_DIR/setupCrontab.sh"
echo ""
echo "3. 查看历史数据:"
echo "   cat $HISTORY_FILE | jq ."
echo ""
echo "4. 监控日志 (设置定时任务后):"
echo "   tail -f /tmp/photo_queue_monitor.log"
