#!/bin/bash

# 检查照片下载队列并发送邮件报告
# 基于你的查询示例: db.reso_photo_download_queue.find({priority:{$gt:35000}}).count()

# 邮件配置
RM_FROM="<EMAIL>"
RM_URL="https://ml1.realmaster.cc/send"

# 参数处理
RM_TO="$1"
THRESHOLD="$2"

# 默认值
if [ -z "$RM_TO" ]; then
    RM_TO="<EMAIL>,<EMAIL>"
fi

if [ -z "$THRESHOLD" ]; then
    THRESHOLD=35000
fi

# MongoDB连接URI (生产环境RNI数据库)
MONGO_URI="***********************************************************************************************************************************************************"

echo "==================== 检查照片下载队列 ===================="
echo "优先级阈值: $THRESHOLD"
echo "收件人: $RM_TO"

# 执行MongoDB查询
echo "正在查询数据库..."

# 查询高优先级任务数量
HIGH_PRIORITY_COUNT=$(mongosh "$MONGO_URI" --quiet --eval "
db.reso_photo_download_queue.find({priority:{\$gt:$THRESHOLD}}).count()
")

# 查询总任务数量  
TOTAL_COUNT=$(mongosh "$MONGO_URI" --quiet --eval "
db.reso_photo_download_queue.find().count()
")

# 检查查询结果
if [ $? -ne 0 ]; then
    echo "MongoDB查询失败!"
    exit 1
fi

echo "查询结果:"
echo "高优先级任务 (priority > $THRESHOLD): $HIGH_PRIORITY_COUNT"
echo "总任务数量: $TOTAL_COUNT"

# 判断是否需要告警
ALERT_NEEDED=false
ALERT_REASON=""

# 如果高优先级任务超过100个，发送告警
if [ "$HIGH_PRIORITY_COUNT" -gt 100 ]; then
    ALERT_NEEDED=true
    ALERT_REASON="高优先级任务数量过多 ($HIGH_PRIORITY_COUNT > 100)"
fi

# 如果总任务数量超过500000，发送告警
if [ "$TOTAL_COUNT" -gt 500000 ]; then
    ALERT_NEEDED=true
    if [ -n "$ALERT_REASON" ]; then
        ALERT_REASON="$ALERT_REASON; 总任务数量过多 ($TOTAL_COUNT > 500000)"
    else
        ALERT_REASON="总任务数量过多 ($TOTAL_COUNT > 500000)"
    fi
fi

# 生成邮件内容
CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')

if [ "$ALERT_NEEDED" = true ]; then
    EMAIL_SUBJECT="🚨 照片下载队列告警"
    rmText="<h2 style='color: red;'>🚨 照片下载队列告警</h2>
<p><strong>告警时间:</strong> $CURRENT_TIME</p>
<p><strong>告警原因:</strong> $ALERT_REASON</p>
<hr>
<h3>队列状态:</h3>
<ul>
<li><strong>高优先级任务 (priority > $THRESHOLD):</strong> <span style='color: red; font-weight: bold;'>$HIGH_PRIORITY_COUNT</span></li>
<li><strong>总任务数量:</strong> <span style='color: red; font-weight: bold;'>$TOTAL_COUNT</span></li>
</ul>
<hr>
<p><strong>建议操作:</strong></p>
<ul>
<li>检查照片下载服务状态</li>
<li>查看服务器资源使用情况</li>
<li>考虑增加处理能力或清理队列</li>
</ul>
<p><em>此告警由自动监控脚本生成</em></p>"
else
    EMAIL_SUBJECT="✅ 照片下载队列状态正常"
    rmText="<h2 style='color: green;'>✅ 照片下载队列状态正常</h2>
<p><strong>检查时间:</strong> $CURRENT_TIME</p>
<hr>
<h3>队列状态:</h3>
<ul>
<li><strong>高优先级任务 (priority > $THRESHOLD):</strong> $HIGH_PRIORITY_COUNT</li>
<li><strong>总任务数量:</strong> $TOTAL_COUNT</li>
</ul>
<p><em>此报告由自动监控脚本生成</em></p>"
fi

# 发送邮件
if [ ${#rmText} -gt 0 ] && [ ${#RM_TO} -gt 0 ]; then
  echo "======================= 发送邮件 ======================="
  
  mail=$(jq -n \
    --arg from "$RM_FROM" \
    --arg to "$RM_TO" \
    --arg subject "$EMAIL_SUBJECT" \
    --arg html "$rmText" \
    '{from: $from, to: $to, subject: $subject, html: $html}')
  
  # 将收件人转换为数组
  newEmail=$(jq -r '.to |= split(",")' <<< "${mail}")
  
  echo "发送邮件: $EMAIL_SUBJECT"
  
  # 发送邮件
  response=$(curl -X POST \
    --header "Content-Type: application/json" \
    --header "X-RM-Auth: RM" \
    -s \
    --data "$newEmail" \
    $RM_URL)
  
  if [ $? -eq 0 ]; then
    echo "邮件发送成功!"
    if [ "$ALERT_NEEDED" = true ]; then
        echo "⚠️  告警邮件已发送"
    else
        echo "ℹ️  状态报告已发送"
    fi
  else
    echo "邮件发送失败!"
    exit 1
  fi
fi

echo "======================= 检查完成 ======================="
exit 0
